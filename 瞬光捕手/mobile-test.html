<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>瞬光捕手 - 移动端测试</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/modern-enhancements.css">
    <link rel="stylesheet" href="styles/responsive.css">
    <style>
        .mobile-info {
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            border-radius: 15px;
            backdrop-filter: var(--glass-blur);
            padding: 20px;
            margin: 20px;
            color: var(--text-primary);
        }
        
        .device-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin: 15px 0;
            font-size: 0.9rem;
        }
        
        .touch-demo {
            width: 100%;
            height: 200px;
            background: #000;
            border: 2px solid var(--neon-blue);
            border-radius: 10px;
            position: relative;
            overflow: hidden;
            margin: 20px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--neon-blue);
            font-size: 1.2rem;
            text-align: center;
        }
        
        .touch-indicator {
            position: absolute;
            width: 30px;
            height: 30px;
            background: radial-gradient(circle, var(--neon-blue), transparent);
            border-radius: 50%;
            pointer-events: none;
            opacity: 0;
            transform: scale(0);
            transition: all 0.3s ease;
        }
        
        .touch-indicator.active {
            opacity: 1;
            transform: scale(1);
            animation: touchRipple 0.6s ease-out;
        }
        
        @keyframes touchRipple {
            0% { transform: scale(0); opacity: 1; }
            100% { transform: scale(3); opacity: 0; }
        }
        
        .performance-meter {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 10px 0;
        }
        
        .meter-bar {
            flex: 1;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
        }
        
        .meter-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--neon-green), var(--neon-blue));
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <!-- 动态粒子背景系统 -->
    <div class="particle-system" id="particle-system"></div>
    
    <div class="container">
        <h1 class="game-title" data-text="移动端测试">移动端测试</h1>
        <p class="game-subtitle">瞬光捕手移动端适配验证</p>
        
        <!-- 设备信息 -->
        <div class="mobile-info">
            <h2 style="color: var(--neon-blue); margin-bottom: 15px;">📱 设备信息</h2>
            <div class="device-info">
                <div><strong>屏幕尺寸:</strong></div>
                <div id="screen-size">检测中...</div>
                <div><strong>设备像素比:</strong></div>
                <div id="device-ratio">检测中...</div>
                <div><strong>触摸支持:</strong></div>
                <div id="touch-support">检测中...</div>
                <div><strong>用户代理:</strong></div>
                <div id="user-agent" style="font-size: 0.8rem; word-break: break-all;">检测中...</div>
            </div>
        </div>
        
        <!-- 性能监控 -->
        <div class="mobile-info">
            <h2 style="color: var(--neon-purple); margin-bottom: 15px;">⚡ 性能监控</h2>
            <div class="performance-meter">
                <span>FPS:</span>
                <div class="meter-bar">
                    <div class="meter-fill" id="fps-meter" style="width: 100%;"></div>
                </div>
                <span id="fps-value">60</span>
            </div>
            <div class="performance-meter">
                <span>粒子数量:</span>
                <div class="meter-bar">
                    <div class="meter-fill" id="particle-meter" style="width: 40%;"></div>
                </div>
                <span id="particle-count">20</span>
            </div>
        </div>
        
        <!-- 触摸测试区域 -->
        <div class="mobile-info">
            <h2 style="color: var(--neon-pink); margin-bottom: 15px;">👆 触摸测试</h2>
            <div class="touch-demo" id="touch-demo">
                点击或触摸此区域测试响应
                <div class="touch-indicator" id="touch-indicator"></div>
            </div>
            <div style="text-align: center; margin-top: 10px;">
                <span>触摸次数: </span>
                <span id="touch-count" style="color: var(--neon-blue); font-weight: bold;">0</span>
            </div>
        </div>
        
        <!-- 响应式测试 -->
        <div class="mobile-info">
            <h2 style="color: var(--neon-green); margin-bottom: 15px;">📐 响应式测试</h2>
            <div class="button-test" style="display: flex; flex-wrap: wrap; gap: 10px;">
                <button class="menu-btn">移动端按钮</button>
                <button class="menu-btn primary">主要按钮</button>
                <button class="small-btn">小按钮</button>
            </div>
        </div>
        
        <!-- 游戏控制测试 -->
        <div class="mobile-info">
            <h2 style="color: var(--neon-orange); margin-bottom: 15px;">🎮 游戏控制</h2>
            <div class="touch-controls" style="display: flex; justify-content: space-around; margin: 20px 0;">
                <div class="touch-area" style="background: var(--glass-bg); border: 1px solid var(--glass-border); border-radius: 10px; display: flex; align-items: center; justify-content: center; backdrop-filter: var(--glass-blur);">
                    暂停
                </div>
                <div class="touch-area" style="background: var(--glass-bg); border: 1px solid var(--glass-border); border-radius: 10px; display: flex; align-items: center; justify-content: center; backdrop-filter: var(--glass-blur);">
                    设置
                </div>
            </div>
        </div>
        
        <!-- 返回主页 -->
        <div style="text-align: center; margin: 30px 0;">
            <a href="index.html" class="menu-btn">返回主游戏</a>
        </div>
    </div>
    
    <script src="js/ui/modern-effects.js"></script>
    <script>
        let touchCount = 0;
        let fps = 60;
        let lastTime = performance.now();
        
        // 设备信息检测
        function detectDeviceInfo() {
            document.getElementById('screen-size').textContent = 
                `${window.innerWidth} × ${window.innerHeight}`;
            document.getElementById('device-ratio').textContent = 
                window.devicePixelRatio || 1;
            document.getElementById('touch-support').textContent = 
                'ontouchstart' in window ? '✅ 支持' : '❌ 不支持';
            document.getElementById('user-agent').textContent = 
                navigator.userAgent.substring(0, 50) + '...';
        }
        
        // 触摸测试
        function setupTouchTest() {
            const touchDemo = document.getElementById('touch-demo');
            const touchIndicator = document.getElementById('touch-indicator');
            const touchCountEl = document.getElementById('touch-count');
            
            function handleTouch(event) {
                event.preventDefault();
                touchCount++;
                touchCountEl.textContent = touchCount;
                
                const rect = touchDemo.getBoundingClientRect();
                const x = (event.touches ? event.touches[0].clientX : event.clientX) - rect.left;
                const y = (event.touches ? event.touches[0].clientY : event.clientY) - rect.top;
                
                touchIndicator.style.left = (x - 15) + 'px';
                touchIndicator.style.top = (y - 15) + 'px';
                touchIndicator.classList.remove('active');
                
                setTimeout(() => {
                    touchIndicator.classList.add('active');
                }, 10);
                
                // 创建现代化点击效果
                if (window.modernEffects) {
                    window.modernEffects.createClickEffect(touchDemo, x + rect.left, y + rect.top);
                }
            }
            
            touchDemo.addEventListener('touchstart', handleTouch, { passive: false });
            touchDemo.addEventListener('click', handleTouch);
        }
        
        // 性能监控
        function updatePerformance() {
            const now = performance.now();
            const delta = now - lastTime;
            fps = Math.round(1000 / delta);
            lastTime = now;
            
            document.getElementById('fps-value').textContent = fps;
            const fpsPercent = Math.min(fps / 60 * 100, 100);
            document.getElementById('fps-meter').style.width = fpsPercent + '%';
            
            // 粒子数量检测
            const particles = document.querySelectorAll('.floating-particle:not([style*="display: none"])');
            const particleCount = particles.length;
            document.getElementById('particle-count').textContent = particleCount;
            const particlePercent = particleCount / 50 * 100;
            document.getElementById('particle-meter').style.width = particlePercent + '%';
            
            requestAnimationFrame(updatePerformance);
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📱 移动端测试页面加载完成');
            detectDeviceInfo();
            setupTouchTest();
            updatePerformance();
            
            // 窗口大小变化时更新信息
            window.addEventListener('resize', detectDeviceInfo);
        });
    </script>
</body>
</html>
