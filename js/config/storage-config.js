/**
 * Split-Second Spark - 存储配置
 * 定义各种存储方案的配置选项
 */

/**
 * 存储配置类
 */
class StorageConfig {
    constructor() {
        this.configs = {
            // 基础本地存储配置
            local: {
                dbName: 'SplitSecondSparkDB',
                dbVersion: 1,
                enableCloudSync: false,
                adapterPriority: ['indexeddb', 'localstorage', 'memory']
            },

            // Firebase 云存储配置
            firebase: {
                dbName: 'SplitSecondSparkDB',
                dbVersion: 1,
                enableCloudSync: true,
                syncInterval: 5 * 60 * 1000, // 5分钟同步一次
                adapterPriority: ['hybrid', 'indexeddb', 'localstorage', 'memory'],
                cloudConfig: {
                    type: 'firebase',
                    config: {
                        // Firebase 配置 - 需要用户提供实际的配置信息
                        apiKey: "your-api-key",
                        authDomain: "your-project.firebaseapp.com",
                        projectId: "your-project-id",
                        storageBucket: "your-project.appspot.com",
                        messagingSenderId: "123456789",
                        appId: "your-app-id"
                    }
                }
            },

            // 高性能本地存储配置
            performance: {
                dbName: 'SplitSecondSparkDB_Performance',
                dbVersion: 1,
                enableCloudSync: false,
                adapterPriority: ['indexeddb'], // 仅使用 IndexedDB 以获得最佳性能
                cacheSize: 1000, // 缓存大小
                compressionEnabled: true // 启用数据压缩
            },

            // 隐私模式配置（仅内存存储）
            privacy: {
                dbName: 'SplitSecondSparkDB_Privacy',
                dbVersion: 1,
                enableCloudSync: false,
                adapterPriority: ['memory'], // 仅使用内存存储，不保存到磁盘
                autoCleanup: true // 自动清理数据
            },

            // 开发调试配置
            debug: {
                dbName: 'SplitSecondSparkDB_Debug',
                dbVersion: 1,
                enableCloudSync: false,
                adapterPriority: ['indexeddb', 'localstorage', 'memory'],
                verboseLogging: true, // 详细日志
                debugMode: true // 调试模式
            }
        };

        // 当前使用的配置
        this.currentConfig = 'local';
        
        console.log('📋 存储配置管理器已初始化');
    }

    /**
     * 获取指定配置
     * @param {string} configName - 配置名称
     * @returns {Object} 配置对象
     */
    getConfig(configName = null) {
        const name = configName || this.currentConfig;
        
        if (!this.configs[name]) {
            console.warn(`⚠️ 配置 "${name}" 不存在，使用默认配置`);
            return this.configs.local;
        }
        
        return { ...this.configs[name] };
    }

    /**
     * 设置当前配置
     * @param {string} configName - 配置名称
     */
    setConfig(configName) {
        if (!this.configs[configName]) {
            throw new Error(`配置 "${configName}" 不存在`);
        }
        
        this.currentConfig = configName;
        console.log(`📋 已切换到配置: ${configName}`);
    }

    /**
     * 添加自定义配置
     * @param {string} name - 配置名称
     * @param {Object} config - 配置对象
     */
    addConfig(name, config) {
        this.configs[name] = { ...config };
        console.log(`📋 已添加自定义配置: ${name}`);
    }

    /**
     * 获取所有可用配置
     * @returns {Array} 配置名称列表
     */
    getAvailableConfigs() {
        return Object.keys(this.configs);
    }

    /**
     * 根据环境自动选择配置
     */
    autoSelectConfig() {
        // 检测是否在隐私模式下
        if (this.isPrivateMode()) {
            this.setConfig('privacy');
            return 'privacy';
        }

        // 检测是否在开发环境
        if (this.isDevelopmentMode()) {
            this.setConfig('debug');
            return 'debug';
        }

        // 检测是否支持高性能存储
        if (this.supportsHighPerformanceStorage()) {
            this.setConfig('performance');
            return 'performance';
        }

        // 默认使用本地存储
        this.setConfig('local');
        return 'local';
    }

    /**
     * 检测是否在隐私模式下
     */
    isPrivateMode() {
        try {
            // 尝试使用 localStorage
            localStorage.setItem('test', 'test');
            localStorage.removeItem('test');
            return false;
        } catch (e) {
            return true;
        }
    }

    /**
     * 检测是否在开发环境
     */
    isDevelopmentMode() {
        return location.hostname === 'localhost' || 
               location.hostname === '127.0.0.1' ||
               location.protocol === 'file:' ||
               localStorage.getItem('debug') === 'true';
    }

    /**
     * 检测是否支持高性能存储
     */
    supportsHighPerformanceStorage() {
        return 'indexedDB' in window && 
               'serviceWorker' in navigator &&
               window.performance && 
               window.performance.memory;
    }

    /**
     * 创建存储服务实例
     * @param {string} configName - 配置名称（可选）
     * @returns {Promise<StorageService>} 存储服务实例
     */
    async createStorageService(configName = null) {
        const config = this.getConfig(configName);
        
        // 根据配置选择存储服务类型
        let StorageServiceClass;
        
        if (config.enableCloudSync) {
            // 使用增强存储服务（支持云同步）
            const { default: EnhancedStorageService } = await import('../utils/enhanced-storage.js');
            StorageServiceClass = EnhancedStorageService;
        } else {
            // 使用基础存储服务
            const { default: StorageService } = await import('../utils/storage.js');
            StorageServiceClass = StorageService;
        }
        
        const storageService = new StorageServiceClass(config);
        await storageService.init();
        
        console.log(`✅ 存储服务已创建，配置: ${configName || this.currentConfig}`);
        return storageService;
    }

    /**
     * 获取存储方案推荐
     */
    getRecommendations() {
        const recommendations = [];

        // 基于用户环境给出推荐
        if (this.isPrivateMode()) {
            recommendations.push({
                config: 'privacy',
                reason: '检测到隐私模式，推荐使用内存存储以保护隐私',
                priority: 'high'
            });
        }

        if (this.isDevelopmentMode()) {
            recommendations.push({
                config: 'debug',
                reason: '检测到开发环境，推荐使用调试配置以便调试',
                priority: 'medium'
            });
        }

        if (this.supportsHighPerformanceStorage()) {
            recommendations.push({
                config: 'performance',
                reason: '设备支持高性能存储，推荐使用性能优化配置',
                priority: 'medium'
            });
        }

        // 检测网络连接
        if (navigator.onLine && 'serviceWorker' in navigator) {
            recommendations.push({
                config: 'firebase',
                reason: '网络连接良好，可以考虑使用云存储同步数据',
                priority: 'low'
            });
        }

        // 如果没有特殊推荐，推荐默认配置
        if (recommendations.length === 0) {
            recommendations.push({
                config: 'local',
                reason: '推荐使用标准本地存储配置',
                priority: 'medium'
            });
        }

        return recommendations;
    }

    /**
     * 显示配置信息
     */
    displayConfigInfo(configName = null) {
        const config = this.getConfig(configName);
        const name = configName || this.currentConfig;
        
        console.group(`📋 存储配置信息: ${name}`);
        console.log('数据库名称:', config.dbName);
        console.log('数据库版本:', config.dbVersion);
        console.log('云同步:', config.enableCloudSync ? '启用' : '禁用');
        console.log('适配器优先级:', config.adapterPriority);
        
        if (config.cloudConfig) {
            console.log('云存储类型:', config.cloudConfig.type);
        }
        
        if (config.syncInterval) {
            console.log('同步间隔:', `${config.syncInterval / 1000}秒`);
        }
        
        console.groupEnd();
    }
}

/**
 * 存储配置管理器单例
 */
class StorageConfigManager {
    constructor() {
        if (StorageConfigManager.instance) {
            return StorageConfigManager.instance;
        }
        
        this.config = new StorageConfig();
        StorageConfigManager.instance = this;
    }

    static getInstance() {
        if (!StorageConfigManager.instance) {
            StorageConfigManager.instance = new StorageConfigManager();
        }
        return StorageConfigManager.instance;
    }

    getConfig(name) {
        return this.config.getConfig(name);
    }

    setConfig(name) {
        return this.config.setConfig(name);
    }

    createStorageService(configName) {
        return this.config.createStorageService(configName);
    }

    autoSelectConfig() {
        return this.config.autoSelectConfig();
    }

    getRecommendations() {
        return this.config.getRecommendations();
    }

    displayConfigInfo(configName) {
        return this.config.displayConfigInfo(configName);
    }
}

// 导出配置类
if (typeof window !== 'undefined') {
    window.StorageConfig = StorageConfig;
    window.StorageConfigManager = StorageConfigManager;
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        StorageConfig,
        StorageConfigManager
    };
}
