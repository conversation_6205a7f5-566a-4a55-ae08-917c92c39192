/**
 * 云存储适配器 - 支持多种云存储服务
 * 可以集成 Firebase、AWS S3、阿里云OSS 等云存储服务
 */

/**
 * Firebase 云存储适配器
 * 需要先引入 Firebase SDK
 */
class FirebaseStorageAdapter {
    constructor(config) {
        this.config = config;
        this.db = null;
        this.auth = null;
        this.isAuthenticated = false;
    }

    /**
     * 初始化 Firebase
     */
    async init() {
        try {
            // 初始化 Firebase 应用
            if (!firebase.apps.length) {
                firebase.initializeApp(this.config);
            }
            
            this.db = firebase.firestore();
            this.auth = firebase.auth();
            
            // 监听认证状态
            return new Promise((resolve, reject) => {
                this.auth.onAuthStateChanged((user) => {
                    if (user) {
                        this.isAuthenticated = true;
                        console.log('✅ Firebase 用户已认证:', user.uid);
                        resolve();
                    } else {
                        // 匿名登录
                        this.auth.signInAnonymously()
                            .then(() => {
                                this.isAuthenticated = true;
                                console.log('✅ Firebase 匿名登录成功');
                                resolve();
                            })
                            .catch(reject);
                    }
                });
            });
        } catch (error) {
            throw new Error(`Firebase 初始化失败: ${error.message}`);
        }
    }

    /**
     * 保存数据到 Firestore
     */
    async put(key, value) {
        if (!this.isAuthenticated) {
            throw new Error('用户未认证');
        }

        try {
            const userId = this.auth.currentUser.uid;
            const docRef = this.db.collection('gameData').doc(userId).collection('data').doc(key);
            
            await docRef.set({
                value: value,
                timestamp: firebase.firestore.FieldValue.serverTimestamp(),
                lastModified: Date.now()
            });
            
            console.log(`📤 数据已保存到云端: ${key}`);
            return true;
        } catch (error) {
            console.error('❌ 云端保存失败:', error);
            throw error;
        }
    }

    /**
     * 从 Firestore 读取数据
     */
    async get(key) {
        if (!this.isAuthenticated) {
            throw new Error('用户未认证');
        }

        try {
            const userId = this.auth.currentUser.uid;
            const docRef = this.db.collection('gameData').doc(userId).collection('data').doc(key);
            const doc = await docRef.get();
            
            if (doc.exists) {
                const data = doc.data();
                console.log(`📥 从云端读取数据: ${key}`);
                return data.value;
            } else {
                return null;
            }
        } catch (error) {
            console.error('❌ 云端读取失败:', error);
            throw error;
        }
    }

    /**
     * 删除云端数据
     */
    async delete(key) {
        if (!this.isAuthenticated) {
            throw new Error('用户未认证');
        }

        try {
            const userId = this.auth.currentUser.uid;
            const docRef = this.db.collection('gameData').doc(userId).collection('data').doc(key);
            await docRef.delete();
            
            console.log(`🗑️ 云端数据已删除: ${key}`);
            return true;
        } catch (error) {
            console.error('❌ 云端删除失败:', error);
            throw error;
        }
    }

    /**
     * 列出所有键值
     */
    async list(prefix = '') {
        if (!this.isAuthenticated) {
            throw new Error('用户未认证');
        }

        try {
            const userId = this.auth.currentUser.uid;
            const collectionRef = this.db.collection('gameData').doc(userId).collection('data');
            const snapshot = await collectionRef.get();
            
            const keys = [];
            snapshot.forEach(doc => {
                if (doc.id.startsWith(prefix)) {
                    keys.push(doc.id);
                }
            });
            
            return keys;
        } catch (error) {
            console.error('❌ 云端列表获取失败:', error);
            throw error;
        }
    }

    /**
     * 同步本地数据到云端
     */
    async syncToCloud(localStorageService) {
        console.log('🔄 开始同步本地数据到云端...');
        
        try {
            // 获取本地所有数据
            const localKeys = await localStorageService.list();
            
            for (const key of localKeys) {
                const value = await localStorageService.get(key);
                await this.put(key, value);
            }
            
            console.log(`✅ 同步完成，共同步 ${localKeys.length} 条数据`);
        } catch (error) {
            console.error('❌ 同步失败:', error);
            throw error;
        }
    }

    /**
     * 从云端同步数据到本地
     */
    async syncFromCloud(localStorageService) {
        console.log('🔄 开始从云端同步数据到本地...');
        
        try {
            const cloudKeys = await this.list();
            
            for (const key of cloudKeys) {
                const value = await this.get(key);
                await localStorageService.put(key, value);
            }
            
            console.log(`✅ 同步完成，共同步 ${cloudKeys.length} 条数据`);
        } catch (error) {
            console.error('❌ 同步失败:', error);
            throw error;
        }
    }

    /**
     * 获取存储统计信息
     */
    async getStats() {
        try {
            const keys = await this.list();
            return {
                type: 'Firebase Cloud Storage',
                keyCount: keys.length,
                isAvailable: this.isAuthenticated,
                userId: this.auth.currentUser?.uid
            };
        } catch (error) {
            return {
                type: 'Firebase Cloud Storage',
                keyCount: 0,
                isAvailable: false,
                error: error.message
            };
        }
    }
}

/**
 * 本地存储 + 云存储混合适配器
 * 提供本地优先，云端备份的存储策略
 */
class HybridStorageAdapter {
    constructor(localAdapter, cloudAdapter) {
        this.localAdapter = localAdapter;
        this.cloudAdapter = cloudAdapter;
        this.syncEnabled = true;
        this.syncInterval = 5 * 60 * 1000; // 5分钟同步一次
        this.syncTimer = null;
    }

    async init() {
        // 初始化本地存储
        await this.localAdapter.init();
        
        // 尝试初始化云存储
        try {
            await this.cloudAdapter.init();
            console.log('✅ 混合存储模式：本地 + 云端');
            
            // 启动定期同步
            this.startAutoSync();
        } catch (error) {
            console.warn('⚠️ 云存储不可用，使用纯本地模式:', error.message);
            this.syncEnabled = false;
        }
    }

    /**
     * 保存数据（本地优先）
     */
    async put(key, value) {
        // 先保存到本地
        await this.localAdapter.put(key, value);
        
        // 如果云存储可用，异步保存到云端
        if (this.syncEnabled) {
            this.cloudAdapter.put(key, value).catch(error => {
                console.warn('⚠️ 云端保存失败，数据仅保存在本地:', error.message);
            });
        }
    }

    /**
     * 读取数据（本地优先）
     */
    async get(key) {
        // 优先从本地读取
        const localValue = await this.localAdapter.get(key);
        if (localValue !== null) {
            return localValue;
        }
        
        // 如果本地没有且云存储可用，从云端读取
        if (this.syncEnabled) {
            try {
                const cloudValue = await this.cloudAdapter.get(key);
                if (cloudValue !== null) {
                    // 将云端数据缓存到本地
                    await this.localAdapter.put(key, cloudValue);
                    return cloudValue;
                }
            } catch (error) {
                console.warn('⚠️ 云端读取失败:', error.message);
            }
        }
        
        return null;
    }

    /**
     * 删除数据
     */
    async delete(key) {
        // 从本地删除
        await this.localAdapter.delete(key);
        
        // 如果云存储可用，也从云端删除
        if (this.syncEnabled) {
            this.cloudAdapter.delete(key).catch(error => {
                console.warn('⚠️ 云端删除失败:', error.message);
            });
        }
    }

    /**
     * 列出键值
     */
    async list(prefix = '') {
        const localKeys = await this.localAdapter.list(prefix);
        
        if (this.syncEnabled) {
            try {
                const cloudKeys = await this.cloudAdapter.list(prefix);
                // 合并本地和云端的键值，去重
                const allKeys = [...new Set([...localKeys, ...cloudKeys])];
                return allKeys;
            } catch (error) {
                console.warn('⚠️ 云端列表获取失败:', error.message);
            }
        }
        
        return localKeys;
    }

    /**
     * 启动自动同步
     */
    startAutoSync() {
        if (!this.syncEnabled) return;
        
        this.syncTimer = setInterval(async () => {
            try {
                await this.syncData();
            } catch (error) {
                console.warn('⚠️ 自动同步失败:', error.message);
            }
        }, this.syncInterval);
        
        console.log(`🔄 自动同步已启动，间隔: ${this.syncInterval / 1000}秒`);
    }

    /**
     * 停止自动同步
     */
    stopAutoSync() {
        if (this.syncTimer) {
            clearInterval(this.syncTimer);
            this.syncTimer = null;
            console.log('⏹️ 自动同步已停止');
        }
    }

    /**
     * 手动同步数据
     */
    async syncData() {
        if (!this.syncEnabled) return;
        
        console.log('🔄 开始数据同步...');
        
        try {
            // 获取本地和云端的所有键值
            const localKeys = await this.localAdapter.list();
            const cloudKeys = await this.cloudAdapter.list();
            
            // 同步本地到云端的新数据
            for (const key of localKeys) {
                if (!cloudKeys.includes(key)) {
                    const value = await this.localAdapter.get(key);
                    await this.cloudAdapter.put(key, value);
                    console.log(`📤 同步到云端: ${key}`);
                }
            }
            
            // 同步云端到本地的新数据
            for (const key of cloudKeys) {
                if (!localKeys.includes(key)) {
                    const value = await this.cloudAdapter.get(key);
                    await this.localAdapter.put(key, value);
                    console.log(`📥 同步到本地: ${key}`);
                }
            }
            
            console.log('✅ 数据同步完成');
        } catch (error) {
            console.error('❌ 数据同步失败:', error);
            throw error;
        }
    }

    /**
     * 获取存储统计信息
     */
    async getStats() {
        const localStats = await this.localAdapter.getStats();
        
        if (this.syncEnabled) {
            try {
                const cloudStats = await this.cloudAdapter.getStats();
                return {
                    type: 'Hybrid Storage (Local + Cloud)',
                    local: localStats,
                    cloud: cloudStats,
                    syncEnabled: this.syncEnabled
                };
            } catch (error) {
                return {
                    type: 'Hybrid Storage (Local Only)',
                    local: localStats,
                    cloud: { error: error.message },
                    syncEnabled: false
                };
            }
        }
        
        return {
            type: 'Local Storage Only',
            local: localStats,
            syncEnabled: false
        };
    }
}

// 导出适配器类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        FirebaseStorageAdapter,
        HybridStorageAdapter
    };
}
