# Split-Second Spark 存储方案使用指南

## 📦 概述

Split-Second Spark 项目提供了灵活的存储解决方案，支持多种存储后端和配置选项，满足不同场景的需求。

## 🗄️ 支持的存储方案

### 1. 本地存储方案

#### IndexedDB（推荐）
- **优点**: 大容量存储，支持复杂数据结构，异步操作
- **缺点**: 较复杂的API，部分旧浏览器不支持
- **适用场景**: 需要存储大量游戏数据的场景

#### localStorage
- **优点**: 简单易用，广泛支持
- **缺点**: 容量限制（通常5-10MB），同步操作可能阻塞UI
- **适用场景**: 存储少量配置和设置数据

#### 内存存储
- **优点**: 速度最快，无容量限制
- **缺点**: 页面刷新后数据丢失
- **适用场景**: 临时数据存储，隐私模式

### 2. 云存储方案

#### Firebase Firestore
- **优点**: 实时同步，跨设备数据共享，自动备份
- **缺点**: 需要网络连接，可能产生费用
- **适用场景**: 需要跨设备同步的用户数据

#### 混合存储（本地+云端）
- **优点**: 结合本地和云端优势，离线可用，自动同步
- **缺点**: 配置较复杂，需要处理同步冲突
- **适用场景**: 最佳用户体验，推荐方案

## 🚀 快速开始

### 基础使用

```javascript
// 1. 导入存储配置管理器
import { StorageConfigManager } from './js/config/storage-config.js';

// 2. 获取配置管理器实例
const configManager = StorageConfigManager.getInstance();

// 3. 自动选择最佳配置
const selectedConfig = configManager.autoSelectConfig();
console.log('自动选择的配置:', selectedConfig);

// 4. 创建存储服务
const storageService = await configManager.createStorageService();

// 5. 使用存储服务
await storageService.put('player-name', '玩家1');
const playerName = await storageService.get('player-name');
console.log('玩家姓名:', playerName);
```

### 使用特定配置

```javascript
// 使用高性能配置
const performanceStorage = await configManager.createStorageService('performance');

// 使用隐私模式配置
const privacyStorage = await configManager.createStorageService('privacy');

// 使用云存储配置
const cloudStorage = await configManager.createStorageService('firebase');
```

## ⚙️ 配置选项详解

### 本地存储配置

```javascript
const localConfig = {
    dbName: 'SplitSecondSparkDB',
    dbVersion: 1,
    enableCloudSync: false,
    adapterPriority: ['indexeddb', 'localstorage', 'memory']
};
```

### Firebase 云存储配置

```javascript
const firebaseConfig = {
    dbName: 'SplitSecondSparkDB',
    dbVersion: 1,
    enableCloudSync: true,
    syncInterval: 5 * 60 * 1000, // 5分钟同步一次
    adapterPriority: ['hybrid', 'indexeddb', 'localstorage', 'memory'],
    cloudConfig: {
        type: 'firebase',
        config: {
            apiKey: "your-api-key",
            authDomain: "your-project.firebaseapp.com",
            projectId: "your-project-id",
            storageBucket: "your-project.appspot.com",
            messagingSenderId: "123456789",
            appId: "your-app-id"
        }
    }
};
```

### 自定义配置

```javascript
// 添加自定义配置
const customConfig = {
    dbName: 'MyCustomDB',
    dbVersion: 2,
    enableCloudSync: false,
    adapterPriority: ['indexeddb'],
    cacheSize: 500,
    compressionEnabled: true
};

configManager.config.addConfig('custom', customConfig);
const customStorage = await configManager.createStorageService('custom');
```

## 🔧 高级功能

### 数据同步

```javascript
// 仅在混合存储模式下可用
if (storageService.hybridMode) {
    // 手动同步数据
    await storageService.syncData();
    
    // 获取同步状态
    const stats = await storageService.getStorageInfo();
    console.log('存储统计:', stats);
}
```

### 批量操作

```javascript
// 批量保存数据
const gameData = {
    'player-level': 10,
    'player-score': 15000,
    'player-achievements': ['first-win', 'speed-demon'],
    'game-settings': {
        volume: 0.8,
        difficulty: 'normal'
    }
};

for (const [key, value] of Object.entries(gameData)) {
    await storageService.put(key, value);
}

// 批量读取数据
const keys = await storageService.list('player-');
const playerData = {};
for (const key of keys) {
    playerData[key] = await storageService.get(key);
}
console.log('玩家数据:', playerData);
```

### 数据迁移

```javascript
// 从旧存储迁移到新存储
async function migrateData(oldStorage, newStorage) {
    console.log('🔄 开始数据迁移...');
    
    const keys = await oldStorage.list();
    let migratedCount = 0;
    
    for (const key of keys) {
        try {
            const value = await oldStorage.get(key);
            await newStorage.put(key, value);
            migratedCount++;
        } catch (error) {
            console.error(`迁移失败 [${key}]:`, error);
        }
    }
    
    console.log(`✅ 数据迁移完成，共迁移 ${migratedCount} 条数据`);
}

// 使用示例
const oldStorage = await configManager.createStorageService('local');
const newStorage = await configManager.createStorageService('firebase');
await migrateData(oldStorage, newStorage);
```

## 🛠️ 故障排除

### 常见问题

#### 1. IndexedDB 初始化失败
```javascript
// 检查浏览器支持
if (!window.indexedDB) {
    console.warn('浏览器不支持 IndexedDB');
    // 自动降级到 localStorage
}

// 检查存储空间
if ('storage' in navigator && 'estimate' in navigator.storage) {
    const estimate = await navigator.storage.estimate();
    console.log('存储空间:', estimate);
}
```

#### 2. 云存储连接失败
```javascript
// 检查网络连接
if (!navigator.onLine) {
    console.warn('网络连接不可用，使用本地存储');
}

// 检查 Firebase 配置
try {
    await storageService.cloudAdapter.init();
} catch (error) {
    console.error('云存储初始化失败:', error);
    // 降级到本地存储
}
```

#### 3. 数据同步冲突
```javascript
// 处理同步冲突
storageService.on('syncConflict', (conflict) => {
    console.log('同步冲突:', conflict);
    
    // 选择解决策略
    if (conflict.local.timestamp > conflict.remote.timestamp) {
        // 使用本地数据
        return conflict.local.value;
    } else {
        // 使用远程数据
        return conflict.remote.value;
    }
});
```

### 调试工具

```javascript
// 启用详细日志
localStorage.setItem('storage-debug', 'true');

// 查看存储统计
const stats = await storageService.getStorageInfo();
console.table(stats);

// 检查数据完整性
const keys = await storageService.list();
for (const key of keys) {
    const value = await storageService.get(key);
    if (value === null) {
        console.warn(`数据丢失: ${key}`);
    }
}
```

## 📊 性能优化

### 最佳实践

1. **选择合适的存储方案**
   - 小量数据：localStorage
   - 大量数据：IndexedDB
   - 跨设备同步：混合存储

2. **优化数据结构**
   ```javascript
   // 好的做法：扁平化数据结构
   await storageService.put('player-level', 10);
   await storageService.put('player-score', 15000);
   
   // 避免：深层嵌套对象
   await storageService.put('player-data', {
       profile: {
           stats: {
               level: 10,
               score: 15000
           }
       }
   });
   ```

3. **批量操作优化**
   ```javascript
   // 使用事务进行批量操作
   const transaction = await storageService.beginTransaction();
   try {
       await transaction.put('key1', 'value1');
       await transaction.put('key2', 'value2');
       await transaction.commit();
   } catch (error) {
       await transaction.rollback();
   }
   ```

4. **缓存策略**
   ```javascript
   // 实现简单的内存缓存
   class CachedStorageService {
       constructor(storageService) {
           this.storage = storageService;
           this.cache = new Map();
       }
       
       async get(key) {
           if (this.cache.has(key)) {
               return this.cache.get(key);
           }
           
           const value = await this.storage.get(key);
           this.cache.set(key, value);
           return value;
       }
       
       async put(key, value) {
           this.cache.set(key, value);
           return await this.storage.put(key, value);
       }
   }
   ```

## 🔒 安全考虑

### 数据加密

```javascript
// 敏感数据加密存储
class EncryptedStorageService {
    constructor(storageService, encryptionKey) {
        this.storage = storageService;
        this.key = encryptionKey;
    }
    
    async put(key, value) {
        const encrypted = this.encrypt(JSON.stringify(value));
        return await this.storage.put(key, encrypted);
    }
    
    async get(key) {
        const encrypted = await this.storage.get(key);
        if (!encrypted) return null;
        
        try {
            const decrypted = this.decrypt(encrypted);
            return JSON.parse(decrypted);
        } catch (error) {
            console.error('解密失败:', error);
            return null;
        }
    }
    
    encrypt(data) {
        // 实现加密逻辑
        return btoa(data); // 简单示例，实际应使用更强的加密
    }
    
    decrypt(data) {
        // 实现解密逻辑
        return atob(data); // 简单示例，实际应使用更强的解密
    }
}
```

### 数据验证

```javascript
// 数据完整性验证
class ValidatedStorageService {
    constructor(storageService) {
        this.storage = storageService;
    }
    
    async put(key, value) {
        const checksum = this.calculateChecksum(value);
        const wrappedValue = {
            data: value,
            checksum: checksum,
            timestamp: Date.now()
        };
        
        return await this.storage.put(key, wrappedValue);
    }
    
    async get(key) {
        const wrappedValue = await this.storage.get(key);
        if (!wrappedValue) return null;
        
        const expectedChecksum = this.calculateChecksum(wrappedValue.data);
        if (wrappedValue.checksum !== expectedChecksum) {
            console.error('数据完整性验证失败:', key);
            return null;
        }
        
        return wrappedValue.data;
    }
    
    calculateChecksum(data) {
        // 简单的校验和计算
        const str = JSON.stringify(data);
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return hash;
    }
}
```

## 📈 监控和分析

### 使用统计

```javascript
// 存储使用情况监控
class StorageMonitor {
    constructor(storageService) {
        this.storage = storageService;
        this.stats = {
            reads: 0,
            writes: 0,
            deletes: 0,
            errors: 0
        };
    }
    
    async get(key) {
        try {
            this.stats.reads++;
            return await this.storage.get(key);
        } catch (error) {
            this.stats.errors++;
            throw error;
        }
    }
    
    async put(key, value) {
        try {
            this.stats.writes++;
            return await this.storage.put(key, value);
        } catch (error) {
            this.stats.errors++;
            throw error;
        }
    }
    
    getStats() {
        return { ...this.stats };
    }
}
```

## 🎯 总结

Split-Second Spark 的存储系统提供了：

1. **灵活性**: 支持多种存储后端和配置
2. **可靠性**: 自动降级和错误恢复机制
3. **性能**: 优化的数据访问和缓存策略
4. **安全性**: 数据加密和完整性验证
5. **易用性**: 简单统一的API接口

选择合适的存储方案可以显著提升游戏的用户体验和数据安全性。建议根据具体需求选择最适合的配置方案。
